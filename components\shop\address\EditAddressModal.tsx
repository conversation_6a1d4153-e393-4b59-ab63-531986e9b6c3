import EditAddressForm from '@/components/Header/EditAddress';
import { DialogHeader } from '@/components/UI/dialog';
import { UserAddress } from '@/lib/types/types';
import { Dialog, DialogContent, DialogTitle } from '@radix-ui/react-dialog';
import { Pencil, Trash } from 'lucide-react';
interface EditAddressModalProps {
    isModalOpen: boolean;
    onClose: () => void;
    addresses: UserAddress[];
    selectedAddressId: string | null;
    setSelectedAddressId: (id: string | null) => void;
    setIsModalOpen: (open: boolean) => void;
    setAddresses: React.Dispatch<React.SetStateAction<UserAddress[]>>;
    modalMode: 'select' | 'edit';
    setModalMode: (mode: 'select' | 'edit') => void;
    addressToEdit: UserAddress | null;
    setAddressToEdit: (address: UserAddress | null) => void;
    handleUpdateAddress: (updatedAddress: UserAddress) => void;
}

const EditAddressModal = ({ isModalOpen, addresses, selectedAddressId, setSelectedAddressId, setIsModalOpen, modalMode, setModalMode, addressToEdit, setAddressToEdit, handleUpdateAddress, setAddresses
}: EditAddressModalProps) => {
    return (
        <div></div>
    )
}

export default EditAddressModal